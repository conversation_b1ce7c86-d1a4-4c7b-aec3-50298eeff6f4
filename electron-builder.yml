productName: 录音器
appId: com.company.recorder
directories:
  output: dist_electron
files:
  - package.json
  - 'electron/main.cjs'
  - 'electron/preload.cjs'
  - 'dist/**/*'
  - './app/**/*'
win:
  target: nsis
  icon: build/icons/icon.ico
  requestedExecutionLevel: requireAdministrator # 需要管理员权限来设置自启动
mac:
  target: dmg
  # icon: 'clearbuild/icons/icon.icns' mac需要icns类型图标
linux:
  target:
    - AppImage
    - deb
  icon: 'build/icons/icon.ico'
nsis:
  oneClick: false
  allowElevation: true # 允许提升权限
  allowToChangeInstallationDirectory: true
  installerIcon: 'build/icons/icon.ico'
  uninstallerIcon: 'build/icons/icon.ico'
  installerHeaderIcon: 'build/icons/icon.ico'
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: '录音器'
  runAfterFinish: true # 安装完成后自动运行
  # 添加自启动注册表项
  include: 'installer.nsh'
