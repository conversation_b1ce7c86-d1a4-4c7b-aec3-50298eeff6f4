// Electron API 类型声明
export interface ElectronAPI {
  // 窗口控制
  minimizeWindow: () => Promise<void>
  closeWindow: () => Promise<void>
  showWindow: () => Promise<void>
  minimizeToDesktop: () => Promise<void>
  restoreMainWindow: () => Promise<void>

  // 窗口拖动
  moveWindow: (deltaX: number, deltaY: number) => Promise<void>

  // 音频文件处理
  uploadAudio: (audioBlob: Blob) => Promise<{
    success: boolean
    message: string
    filePath?: string
    filename?: string
    buffer?: number[]
    size?: number
  }>

  // 获取URL参数
  getUrlParams: () => URLSearchParams
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export { }
