<template>
  <div class="main-recorder">
    <!-- 标题栏 - 使用系统原生控制按钮 -->
    <div class="title-bar">
      <h1 class="title">录音器</h1>
      <button class="minimize-btn" @click="minimizeToDesktop" title="最小化到桌面组件">
        <svg viewBox="0 0 24 24" class="icon">
          <path d="M19 13H5v-2h14v2z" />
        </svg>
      </button>
    </div>

    <!-- 主录音区域 -->
    <div class="recorder-area">
      <!-- 录音状态显示 -->
      <div class="status-display">
        <div class="recording-indicator" :class="{ active: recorderStore.isRecording }">
          <div class="pulse-ring"></div>
          <div class="recording-dot"></div>
        </div>

        <div class="status-text">
          <h2 v-if="!recorderStore.isRecording && recorderStore.recordingDuration === 0">
            准备录音
          </h2>
          <h2 v-else-if="recorderStore.isRecording">正在录音...</h2>
          <h2 v-else-if="recorderStore.isProcessing">处理中...</h2>
          <h2 v-else>录音完成</h2>
        </div>

        <!-- 录音时长 -->
        <div
          class="duration-display"
          v-if="recorderStore.isRecording || recorderStore.recordingDuration > 0"
        >
          <span class="duration">{{ formatDuration(recorderStore.recordingDuration) }}</span>
        </div>
      </div>

      <!-- 录音控制按钮 -->
      <div class="controls">
        <button
          class="record-btn"
          :class="{ recording: recorderStore.isRecording, disabled: recorderStore.isProcessing }"
          @click="toggleRecording"
          :disabled="recorderStore.isProcessing"
        >
          <svg v-if="!recorderStore.isRecording" viewBox="0 0 24 24" class="icon">
            <circle cx="12" cy="12" r="8" />
          </svg>
          <svg v-else viewBox="0 0 24 24" class="icon">
            <rect x="8" y="8" width="8" height="8" rx="1" />
          </svg>
          <span class="btn-text">
            {{ recorderStore.isRecording ? '停止录音' : '开始录音' }}
          </span>
        </button>
      </div>

      <!-- 录音波形显示 -->
      <div class="waveform" v-if="recorderStore.isRecording">
        <div
          class="wave-bar"
          v-for="i in 20"
          :key="i"
          :style="{ animationDelay: i * 0.1 + 's' }"
        ></div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p class="tip">点击最小化按钮可缩小到桌面小图标</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { RecordingState } from '@/constants/app.constants'
import { useRecorderStore } from '@/stores/counter'

// 使用全局状态管理
const recorderStore = useRecorderStore()

// 录音相关状态 - 只在主窗口初始化一次
const { audioState, recOpen, recStop } = useRecorder({
  onRecordingComplete: async (blob: Blob, duration: number) => {
    console.log('录音完成，准备上传...', duration)
    await uploadAudio(blob)
  },
})

// 将录音器实例保存到全局状态
recorderStore.setRecorderInstance({ audioState, recOpen, recStop })

// 音频处理函数
const uploadAudio = async (audioBlob: Blob) => {
  try {
    recorderStore.setProcessing(true)

    if (window.electronAPI) {
      const result = await window.electronAPI.uploadAudio(audioBlob)
      console.log('音频文件已准备完成:', result)

      if (result.success) {
        // 这里你可以调用你的接口进行上传
        // 可以使用 result.buffer, result.filePath, result.filename 等信息
        console.log('音频文件信息:', {
          filename: result.filename,
          filePath: result.filePath,
          size: result.size,
        })

        // 示例：调用你的上传接口
        // await uploadToYourAPI(result.buffer, result.filename)
      }
    }
  } catch (error) {
    console.error('音频处理失败:', error)
  } finally {
    recorderStore.setProcessing(false)
  }
}

// 监听录音状态变化
const updateRecordingState = () => {
  recorderStore.setRecordingState(audioState.value)

  if (recorderStore.isRecording) {
    recorderStore.startDurationTimer()
  } else {
    recorderStore.stopDurationTimer()
  }
}

// 格式化时长显示
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 切换录音状态
const toggleRecording = async () => {
  if (recorderStore.isProcessing) return

  try {
    recorderStore.setProcessing(true)

    if (!recorderStore.isRecording) {
      await recOpen()
    } else {
      recStop()
      setTimeout(() => {
        recorderStore.resetDuration()
      }, 2000)
    }
  } catch (error) {
    console.error('录音操作失败:', error)
  } finally {
    recorderStore.setProcessing(false)
  }
}

// 窗口控制
const minimizeToDesktop = () => {
  if (window.electronAPI) {
    window.electronAPI.minimizeToDesktop()
  }
}

// 监听录音状态变化
onMounted(() => {
  const stopWatching = watch(
    audioState,
    (newState) => {
      updateRecordingState()
    },
    { immediate: true },
  )

  onUnmounted(() => {
    stopWatching()
    stopDurationTimer()
  })
})
</script>

<style scoped>
.main-recorder {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.minimize-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.minimize-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.minimize-btn .icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.recorder-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.status-display {
  text-align: center;
  margin-bottom: 40px;
}

.recording-indicator {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
}

.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  opacity: 0;
}

.recording-indicator.active .pulse-ring {
  animation: pulse 2s infinite;
}

.recording-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: #ff3b30;
  border-radius: 50%;
  opacity: 0.7;
}

.recording-indicator.active .recording-dot {
  animation: blink 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.status-text h2 {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 10px 0;
}

.duration-display {
  margin-top: 15px;
}

.duration {
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: 32px;
  font-weight: 600;
  color: #ffeb3b;
}

.controls {
  margin-bottom: 40px;
}

.record-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 30px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.record-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.record-btn.recording {
  background: rgba(255, 59, 48, 0.2);
}

.record-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.record-btn .icon {
  width: 32px;
  height: 32px;
  fill: currentColor;
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
}

.waveform {
  display: flex;
  align-items: center;
  gap: 3px;
  height: 40px;
}

.wave-bar {
  width: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  animation: wave 1.5s infinite ease-in-out;
}

@keyframes wave {
  0%,
  100% {
    height: 10px;
  }
  50% {
    height: 30px;
  }
}

.footer {
  padding: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
}

.tip {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}
</style>
