<script setup lang="ts">
import { ref, onMounted } from 'vue'
import RecorderWidget from '@/components/RecorderWidget.vue'
import MainRecorder from '@/components/MainRecorder.vue'

const isMiniMode = ref(false)

onMounted(() => {
  // 检查URL参数来判断是否为小窗口模式
  const urlParams = new URLSearchParams(window.location.search)
  isMiniMode.value = urlParams.has('mini')
})
</script>

<template>
  <div class="app">
    <MainRecorder v-if="!isMiniMode" />
    <RecorderWidget v-else />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
}

#app {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}
</style>
