{"name": "wait-on", "description": "wait-on is a cross platform command line utility and Node.js API which will wait for files, ports, sockets, and http(s) resources to become available", "version": "8.0.3", "type": "commonjs", "main": "lib/wait-on", "bin": {"wait-on": "bin/wait-on"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/jeffbski/wait-on.git"}, "bugs": {"url": "http://github.com/jeffbski/wait-on/issues"}, "license": "MIT", "scripts": {"lint": "eslint \"lib/**/*.js\" \"test/**/*.js\" \"bin/wait-on\"", "publish:next": "npm publish --tag next && npm view", "test": "npm run lint && npm run test:mocha", "test:mocha": "mocha --exit 'test/**/*.mocha.js'"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"eslint": "^9.22.0", "eslint-plugin-chai-friendly": "^1.0.1", "expect-legacy": "^1.20.2", "mkdirp": "^1.0.4", "mocha": "^10.7.3", "temp": "^0.9.4"}, "dependencies": {"axios": "^1.8.2", "joi": "^17.13.3", "lodash": "^4.17.21", "minimist": "^1.2.8", "rxjs": "^7.8.2"}, "keywords": ["wait", "delay", "cli", "files", "tcp", "ports", "sockets", "http", "exist", "ready", "available", "portable", "cross-platform", "unix", "linux", "windows", "win32", "osx"]}