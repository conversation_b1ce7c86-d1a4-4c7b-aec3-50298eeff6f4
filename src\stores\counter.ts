import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { RecordingState } from '@/constants/app.constants'

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0)
  const doubleCount = computed(() => count.value * 2)
  function increment() {
    count.value++
  }

  return { count, doubleCount, increment }
})

// 全局录音状态管理
export const useRecorderStore = defineStore('recorder', () => {
  // 录音状态
  const recordingState = ref<RecordingState>(RecordingState.STOPPED)
  const isRecording = computed(() => recordingState.value === RecordingState.RECORDING)
  const isProcessing = ref(false)
  const recordingDuration = ref(0)

  // 录音器实例和计时器
  let recorderInstance: any = null
  let durationTimer: number | null = null

  // 更新录音状态
  const setRecordingState = (state: RecordingState) => {
    recordingState.value = state
  }

  // 设置处理状态
  const setProcessing = (processing: boolean) => {
    isProcessing.value = processing
  }

  // 更新录音时长
  const setRecordingDuration = (duration: number) => {
    recordingDuration.value = duration
  }

  // 重置录音时长
  const resetDuration = () => {
    recordingDuration.value = 0
  }

  // 开始录音时长计时
  const startDurationTimer = () => {
    resetDuration()
    durationTimer = setInterval(() => {
      recordingDuration.value++
    }, 1000)
  }

  // 停止录音时长计时
  const stopDurationTimer = () => {
    if (durationTimer) {
      clearInterval(durationTimer)
      durationTimer = null
    }
  }

  // 设置录音器实例
  const setRecorderInstance = (instance: any) => {
    recorderInstance = instance
  }

  // 获取录音器实例
  const getRecorderInstance = () => {
    return recorderInstance
  }

  return {
    recordingState,
    isRecording,
    isProcessing,
    recordingDuration,
    setRecordingState,
    setProcessing,
    setRecordingDuration,
    resetDuration,
    startDurationTimer,
    stopDurationTimer,
    setRecorderInstance,
    getRecorderInstance
  }
})
